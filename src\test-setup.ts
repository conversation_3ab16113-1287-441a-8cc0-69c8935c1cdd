// Test setup file for Jest
import { config } from '@/config';
import { logger, LogLevel } from '@/utils/logger';

// Set up test environment
beforeAll(() => {
  // Set log level to error to reduce noise during tests
  logger.setLogLevel(LogLevel.ERROR);
  
  // Ensure test directories exist
  config.ensureDirectories();
});

// Clean up after tests
afterAll(() => {
  // Clean up any test data if needed
});

// Mock console methods to reduce noise during tests
global.console = {
  ...console,
  log: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};
