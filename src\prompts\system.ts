import { readFileSync } from 'fs';
import { join } from 'path';
import {
  JsonPromptTemplate,
  JsonPromptTool,
  AgentMessage,
  Tool,
  ToolExecutionResult,
  PromptContext,
  ExecutionContext
} from '@/types';
import { logger } from '@/utils/logger';

export class PromptSystem {
  private promptTemplate: JsonPromptTemplate | null = null;
  private isEnabled: boolean = false;
  private templatePath: string;

  constructor() {
    // Try to find the template file in multiple locations
    const possiblePaths = [
      join(__dirname, 'agentic-assistant.json'),
      join(__dirname, '..', 'prompts', 'agentic-assistant.json'),
      join(process.cwd(), 'src', 'prompts', 'agentic-assistant.json'),
      join(process.cwd(), 'prompts', 'agentic-assistant.json')
    ];

    const foundPath = possiblePaths.find(path => {
      try {
        require('fs').accessSync(path);
        return true;
      } catch {
        return false;
      }
    });

    this.templatePath = foundPath ?? possiblePaths[0] ?? join(__dirname, 'agentic-assistant.json');
  }

  public async initialize(): Promise<void> {
    try {
      logger.info('Initializing prompt system', undefined, 'PromptSystem');
      
      // Load the JSON prompt template
      const templateContent = readFileSync(this.templatePath, 'utf-8');
      this.promptTemplate = JSON.parse(templateContent) as JsonPromptTemplate;
      
      this.isEnabled = true;
      
      logger.info('Prompt system initialized successfully', {
        templateName: this.promptTemplate.name,
        version: this.promptTemplate.version,
        messagesCount: this.promptTemplate.messages.length,
        toolsCount: this.promptTemplate.tools.length
      }, 'PromptSystem');
    } catch (error) {
      logger.error('Failed to initialize prompt system', error, 'PromptSystem');
      this.isEnabled = false;
      throw error;
    }
  }

  public isPromptSystemEnabled(): boolean {
    return this.isEnabled && this.promptTemplate !== null;
  }

  public getTemplate(): JsonPromptTemplate | null {
    return this.promptTemplate;
  }

  public processMessages(
    userMessages: AgentMessage[], 
    context: ExecutionContext,
    promptContext?: PromptContext
  ): AgentMessage[] {
    if (!this.isPromptSystemEnabled() || !this.promptTemplate) {
      logger.debug('Prompt system disabled, returning messages as-is', undefined, 'PromptSystem');
      return userMessages;
    }

    try {
      // Convert JSON prompt messages to AgentMessage format
      const systemMessages: AgentMessage[] = this.promptTemplate.messages.map(msg => ({
        role: msg.role,
        content: this.processMessageContent(msg.content, context, promptContext)
      }));

      // Combine system messages with user messages
      // System messages go first, then user messages
      const processedMessages = [...systemMessages, ...userMessages];

      logger.debug('Messages processed through prompt system', {
        systemMessages: systemMessages.length,
        userMessages: userMessages.length,
        totalMessages: processedMessages.length
      }, 'PromptSystem');

      return processedMessages;
    } catch (error) {
      logger.error('Error processing messages through prompt system', error, 'PromptSystem');
      // Fallback to original messages if processing fails
      return userMessages;
    }
  }

  public getToolsFromTemplate(): JsonPromptTool[] {
    if (!this.isPromptSystemEnabled() || !this.promptTemplate) {
      return [];
    }

    return this.promptTemplate.tools;
  }

  public validateAndExecuteTool(
    toolCall: { name: string; parameters: unknown },
    context: ExecutionContext
  ): Promise<ToolExecutionResult> {
    // Import toolRegistry dynamically to avoid circular dependency
    const { toolRegistry } = require('@/tools');

    // Get tool from Zod registry for execution
    const tool = toolRegistry.getTool(toolCall.name);
    if (!tool) {
      throw new Error(`Tool not found: ${toolCall.name}`);
    }

    // Validate parameters using Zod schema
    const validationResult = tool.parameters.safeParse(toolCall.parameters);
    if (!validationResult.success) {
      throw new Error(`Invalid parameters: ${validationResult.error.message}`);
    }

    // Execute with validated parameters
    return tool.execute(validationResult.data, context);
  }

  public validateToolsCompatibility(availableTools: Tool[]): boolean {
    if (!this.isPromptSystemEnabled() || !this.promptTemplate) {
      return true;
    }

    try {
      const templateToolNames = this.promptTemplate.tools.map(tool => tool.function.name);
      const availableToolNames = availableTools.map(tool => tool.name);

      // Check if all template tools are available
      const missingTools = templateToolNames.filter(name => !availableToolNames.includes(name));

      if (missingTools.length > 0) {
        logger.warn('Some tools from template are not available', {
          missingTools,
          templateTools: templateToolNames.length,
          availableTools: availableToolNames.length
        }, 'PromptSystem');
        return false;
      }

      logger.debug('All template tools are available', {
        templateTools: templateToolNames.length,
        availableTools: availableToolNames.length
      }, 'PromptSystem');

      return true;
    } catch (error) {
      logger.error('Error validating tools compatibility', error, 'PromptSystem');
      return false;
    }
  }

  public getToolDefinitionsForLLM(): JsonPromptTool[] {
    if (!this.isPromptSystemEnabled() || !this.promptTemplate) {
      return [];
    }

    return this.promptTemplate.tools;
  }

  public convertZodToolsToJsonSchema(zodTools: Tool[]): JsonPromptTool[] {
    // Import DeepseekProvider to use its conversion method
    const { DeepseekProvider } = require('@/providers/deepseek');
    const provider = new DeepseekProvider();

    return zodTools.map(tool => ({
      type: 'function' as const,
      function: {
        name: tool.name,
        description: tool.description,
        parameters: provider.zodToJsonSchema ? provider.zodToJsonSchema(tool.parameters) : {
          type: 'object',
          properties: {},
          required: []
        }
      }
    }));
  }

  public getTemplateConfiguration(): { temperature: number; model: string } | null {
    if (!this.isPromptSystemEnabled() || !this.promptTemplate) {
      return null;
    }

    return {
      temperature: this.promptTemplate.temperature,
      model: this.promptTemplate.model
    };
  }

  private processMessageContent(
    content: string, 
    context: ExecutionContext,
    promptContext?: PromptContext
  ): string {
    try {
      let processedContent = content;

      // Replace context variables in the content
      if (promptContext) {
        // Replace user info variables
        if (promptContext.userInfo) {
          processedContent = processedContent.replace(/\{user\.os\}/g, promptContext.userInfo.os);
          processedContent = processedContent.replace(/\{user\.shell\}/g, promptContext.userInfo.shell);
          processedContent = processedContent.replace(/\{user\.workspace\}/g, promptContext.userInfo.workspace);
        }

        // Replace project info variables
        if (promptContext.projectInfo) {
          processedContent = processedContent.replace(/\{project\.type\}/g, promptContext.projectInfo.type);
          processedContent = processedContent.replace(/\{project\.files\}/g, String(promptContext.projectInfo.structure.totalFiles));
          processedContent = processedContent.replace(/\{project\.directories\}/g, String(promptContext.projectInfo.structure.totalDirectories));
        }

        // Replace session info variables
        if (promptContext.sessionInfo) {
          processedContent = processedContent.replace(/\{session\.id\}/g, promptContext.sessionInfo.id);
          processedContent = processedContent.replace(/\{session\.messageCount\}/g, String(promptContext.sessionInfo.messageCount));
          processedContent = processedContent.replace(/\{session\.uptime\}/g, String(Math.round(promptContext.sessionInfo.uptime / 60)));
        }

        // Replace custom variables
        Object.entries(promptContext.variables).forEach(([key, value]) => {
          const regex = new RegExp(`\\{${key}\\}`, 'g');
          processedContent = processedContent.replace(regex, String(value));
        });
      }

      // Replace basic context variables
      processedContent = processedContent.replace(/\{workingDirectory\}/g, context.workingDirectory);
      processedContent = processedContent.replace(/\{sessionId\}/g, context.sessionId);

      return processedContent;
    } catch (error) {
      logger.error('Error processing message content', error, 'PromptSystem');
      return content; // Return original content if processing fails
    }
  }

  public renderSystemPrompt(_templateId: string = 'agentic-assistant', variables: Record<string, unknown> = {}): string {
    if (!this.isPromptSystemEnabled() || !this.promptTemplate) {
      throw new Error('Prompt system not enabled or template not loaded');
    }

    try {
      // Find the system message in the template
      const systemMessage = this.promptTemplate.messages.find(msg => msg.role === 'system');
      
      if (!systemMessage) {
        throw new Error('No system message found in template');
      }

      let renderedPrompt = systemMessage.content;

      // Replace variables in the prompt
      Object.entries(variables).forEach(([key, value]) => {
        const regex = new RegExp(`\\{${key}\\}`, 'g');
        renderedPrompt = renderedPrompt.replace(regex, String(value));
      });

      return renderedPrompt;
    } catch (error) {
      logger.error('Error rendering system prompt', error, 'PromptSystem');
      throw error;
    }
  }

  public updatePromptContext(context: PromptContext): void {
    // This method can be used to update the prompt context
    // For now, it's a placeholder for future enhancements
    logger.debug('Prompt context updated', {
      hasUserInfo: !!context.userInfo,
      hasProjectInfo: !!context.projectInfo,
      hasSessionInfo: !!context.sessionInfo,
      variableCount: Object.keys(context.variables).length
    }, 'PromptSystem');
  }

  public getPromptStats(): {
    enabled: boolean;
    templateName?: string;
    version?: string;
    messagesCount?: number;
    toolsCount?: number;
  } {
    if (!this.isPromptSystemEnabled() || !this.promptTemplate) {
      return { enabled: false };
    }

    return {
      enabled: true,
      templateName: this.promptTemplate.name,
      version: this.promptTemplate.version,
      messagesCount: this.promptTemplate.messages.length,
      toolsCount: this.promptTemplate.tools.length
    };
  }
}

// Export singleton instance
export const promptSystem = new PromptSystem();
