import { LLMProvider } from '@/types';
import { DeepseekProvider } from './deepseek';
import { OllamaProvider } from './ollama';

export class ProviderManager {
  private providers: Map<string, LLMProvider> = new Map();

  constructor() {
    this.registerProvider(new DeepseekProvider());
    this.registerProvider(new OllamaProvider());
  }

  public registerProvider(provider: LLMProvider): void {
    this.providers.set(provider.name, provider);
  }

  public getProvider(name: string): LLMProvider | undefined {
    return this.providers.get(name);
  }

  public getAvailableProviders(): string[] {
    return Array.from(this.providers.keys());
  }

  public hasProvider(name: string): boolean {
    return this.providers.has(name);
  }
}

export const providerManager = new ProviderManager();

export { DeepseekProvider, OllamaProvider };
